
import React, { useState, useEffect, useCallback } from 'react';
import { Z<PERSON>, Egg, <PERSON>, <PERSON>rk<PERSON>, <PERSON>, Trophy } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';

interface Position {
  x: number;
  y: number;
}

interface GameState {
  snake: Position[];
  egg: Position;
  direction: 'UP' | 'DOWN' | 'LEFT' | 'RIGHT';
  score: number;
  gameOver: boolean;
  gameStarted: boolean;
}

interface Particle {
  x: number;
  y: number;
  id: string;
  type: 'sparkle' | 'star' | 'explosion';
}

interface FloatingText {
  x: number;
  y: number;
  text: string;
  id: string;
}

const GRID_SIZE = 20;
const INITIAL_SNAKE = [{ x: 10, y: 10 }];
const INITIAL_EGG = { x: 15, y: 15 };

const SnakeGame = () => {
  const [gameState, setGameState] = useState<GameState>({
    snake: INITIAL_SNAKE,
    egg: INITIAL_EGG,
    direction: 'RIGHT',
    score: 0,
    gameOver: false,
    gameStarted: false,
  });

  const [particles, setParticles] = useState<Particle[]>([]);
  const [floatingTexts, setFloatingTexts] = useState<FloatingText[]>([]);
  const [comboCount, setComboCount] = useState(0);
  const [lastEggTime, setLastEggTime] = useState(0);

  const generateEgg = useCallback((snake: Position[]): Position => {
    let newEgg;
    do {
      newEgg = {
        x: Math.floor(Math.random() * GRID_SIZE),
        y: Math.floor(Math.random() * GRID_SIZE),
      };
    } while (snake.some(segment => segment.x === newEgg.x && segment.y === newEgg.y));
    return newEgg;
  }, []);

  const createParticles = useCallback((position: Position, type: 'sparkle' | 'star' | 'explosion' = 'sparkle') => {
    const particleCount = type === 'explosion' ? 12 : 8;
    const newParticles = Array.from({ length: particleCount }, (_, i) => ({
      x: position.x + (Math.random() - 0.5) * 3,
      y: position.y + (Math.random() - 0.5) * 3,
      id: `${Date.now()}-${i}`,
      type,
    }));
    setParticles(prev => [...prev, ...newParticles]);
    setTimeout(() => {
      setParticles(prev => prev.filter(p => !newParticles.some(np => np.id === p.id)));
    }, 1000);
  }, []);

  const createFloatingText = useCallback((position: Position, text: string) => {
    const floatingText = {
      x: position.x,
      y: position.y,
      text,
      id: `text-${Date.now()}`,
    };
    setFloatingTexts(prev => [...prev, floatingText]);
    setTimeout(() => {
      setFloatingTexts(prev => prev.filter(ft => ft.id !== floatingText.id));
    }, 1500);
  }, []);

  const moveSnake = useCallback(() => {
    if (gameState.gameOver || !gameState.gameStarted) return;

    setGameState(prevState => {
      const { snake, direction, egg, score } = prevState;
      const head = snake[0];
      let newHead: Position;

      switch (direction) {
        case 'UP':
          newHead = { x: head.x, y: head.y - 1 };
          break;
        case 'DOWN':
          newHead = { x: head.x, y: head.y + 1 };
          break;
        case 'LEFT':
          newHead = { x: head.x - 1, y: head.y };
          break;
        case 'RIGHT':
          newHead = { x: head.x + 1, y: head.y };
          break;
        default:
          newHead = head;
      }

      // Check wall collision
      if (newHead.x < 0 || newHead.x >= GRID_SIZE || newHead.y < 0 || newHead.y >= GRID_SIZE) {
        createParticles(head, 'explosion');
        return { ...prevState, gameOver: true };
      }

      // Check self collision
      if (snake.some(segment => segment.x === newHead.x && segment.y === newHead.y)) {
        createParticles(head, 'explosion');
        return { ...prevState, gameOver: true };
      }

      const newSnake = [newHead, ...snake];

      // Check egg collision
      if (newHead.x === egg.x && newHead.y === egg.y) {
        const currentTime = Date.now();
        const timeDiff = currentTime - lastEggTime;
        
        // Combo system
        let newComboCount = comboCount;
        if (timeDiff < 2000) {
          newComboCount = comboCount + 1;
        } else {
          newComboCount = 1;
        }
        setComboCount(newComboCount);
        setLastEggTime(currentTime);

        // Score calculation with combo multiplier
        const baseScore = 10;
        const comboMultiplier = Math.min(newComboCount, 5);
        const earnedScore = baseScore * comboMultiplier;

        createParticles(egg, 'star');
        createFloatingText(egg, `+${earnedScore}${newComboCount > 1 ? ` x${comboMultiplier}` : ''}`);
        
        const newEgg = generateEgg(newSnake);
        return {
          ...prevState,
          snake: newSnake,
          egg: newEgg,
          score: score + earnedScore,
        };
      }

      // Remove tail if no egg eaten
      newSnake.pop();

      return {
        ...prevState,
        snake: newSnake,
      };
    });
  }, [gameState.gameOver, gameState.gameStarted, generateEgg, createParticles, createFloatingText, comboCount, lastEggTime]);

  const changeDirection = useCallback((newDirection: 'UP' | 'DOWN' | 'LEFT' | 'RIGHT') => {
    setGameState(prevState => {
      const { direction } = prevState;
      // Prevent reverse direction
      if (
        (direction === 'UP' && newDirection === 'DOWN') ||
        (direction === 'DOWN' && newDirection === 'UP') ||
        (direction === 'LEFT' && newDirection === 'RIGHT') ||
        (direction === 'RIGHT' && newDirection === 'LEFT')
      ) {
        return prevState;
      }
      return { ...prevState, direction: newDirection };
    });
  }, []);

  const handleKeyPress = useCallback((event: KeyboardEvent) => {
    if (!gameState.gameStarted) return;
    
    switch (event.key) {
      case 'ArrowUp':
      case 'w':
      case 'W':
        event.preventDefault();
        changeDirection('UP');
        break;
      case 'ArrowDown':
      case 's':
      case 'S':
        event.preventDefault();
        changeDirection('DOWN');
        break;
      case 'ArrowLeft':
      case 'a':
      case 'A':
        event.preventDefault();
        changeDirection('LEFT');
        break;
      case 'ArrowRight':
      case 'd':
      case 'D':
        event.preventDefault();
        changeDirection('RIGHT');
        break;
    }
  }, [changeDirection, gameState.gameStarted]);

  const startGame = () => {
    setGameState({
      snake: INITIAL_SNAKE,
      egg: INITIAL_EGG,
      direction: 'RIGHT',
      score: 0,
      gameOver: false,
      gameStarted: true,
    });
    setParticles([]);
    setFloatingTexts([]);
    setComboCount(0);
    setLastEggTime(0);
  };

  const resetGame = () => {
    setGameState({
      snake: INITIAL_SNAKE,
      egg: INITIAL_EGG,
      direction: 'RIGHT',
      score: 0,
      gameOver: false,
      gameStarted: false,
    });
    setParticles([]);
    setFloatingTexts([]);
    setComboCount(0);
    setLastEggTime(0);
  };

  useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [handleKeyPress]);

  useEffect(() => {
    const gameInterval = setInterval(moveSnake, 150);
    return () => clearInterval(gameInterval);
  }, [moveSnake]);

  // Helper function to get snake segment style
  const getSnakeSegmentStyle = (index: number, isHead: boolean) => {
    if (isHead) {
      return "bg-gradient-to-br from-emerald-300 via-green-400 to-emerald-600 border-4 border-emerald-800 shadow-2xl transform scale-110 animate-pulse-glow relative overflow-hidden rounded-xl";
    } else {
      const opacity = Math.max(0.7, 1 - (index * 0.08));
      return `bg-gradient-to-br from-green-300 via-green-500 to-green-700 border-2 border-green-800 shadow-lg transform transition-all duration-300 relative overflow-hidden rounded-lg opacity-${Math.floor(opacity * 100)}`;
    }
  };

  const getScoreRank = (score: number) => {
    if (score >= 500) return { rank: 'Master', icon: Crown, color: 'text-yellow-400' };
    if (score >= 300) return { rank: 'Expert', icon: Trophy, color: 'text-purple-400' };
    if (score >= 150) return { rank: 'Pro', icon: Star, color: 'text-blue-400' };
    return { rank: 'Beginner', icon: Sparkles, color: 'text-green-400' };
  };

  const scoreRank = getScoreRank(gameState.score);

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-10 left-10 w-32 h-32 bg-gradient-to-br from-yellow-400/20 to-orange-500/20 rounded-full animate-float blur-xl"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-gradient-to-br from-pink-400/20 to-purple-500/20 rounded-full animate-bounce-gentle blur-xl"></div>
        <div className="absolute bottom-32 left-1/4 w-40 h-40 bg-gradient-to-br from-green-400/20 to-blue-500/20 rounded-full animate-pulse blur-xl"></div>
        <div className="absolute bottom-20 right-1/3 w-28 h-28 bg-gradient-to-br from-cyan-400/20 to-teal-500/20 rounded-full animate-wiggle blur-xl"></div>
      </div>

      <div className="relative z-10 flex items-center justify-center p-4 min-h-screen">
        <div className="w-full max-w-5xl">
          {/* Animated Header */}
          <div className="text-center mb-8 relative">
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-full h-20 bg-gradient-to-r from-transparent via-white/10 to-transparent rounded-full blur-2xl animate-pulse-glow"></div>
            </div>
            <h1 className="text-6xl md:text-7xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 via-pink-500 to-purple-600 mb-4 animate-bounce-gentle relative z-10">
              🐍 SNAKE PARADISE 🥚
            </h1>
            <p className="text-2xl text-white/90 animate-slide-up font-medium">
              Collect golden treasures and become the ultimate serpent!
            </p>
          </div>

          {/* Enhanced Score Display */}
          <div className="text-center mb-6 relative">
            <div className="inline-block relative">
              <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/30 via-orange-500/30 to-red-500/30 rounded-full blur-xl animate-pulse-glow"></div>
              <div className="relative bg-gradient-to-r from-white/20 to-white/10 backdrop-blur-xl rounded-full px-8 py-4 border border-white/20">
                <div className="flex items-center justify-center space-x-4">
                  <scoreRank.icon className={`w-8 h-8 ${scoreRank.color} animate-bounce-gentle`} />
                  <div className="text-center">
                    <div className="text-4xl font-bold text-white mb-1">
                      {gameState.score}
                    </div>
                    <div className={`text-sm font-medium ${scoreRank.color}`}>
                      {scoreRank.rank}
                    </div>
                  </div>
                  {comboCount > 1 && (
                    <div className="bg-gradient-to-r from-orange-400 to-red-500 text-white px-3 py-1 rounded-full text-sm font-bold animate-bounce-gentle">
                      {comboCount}x COMBO!
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced Game Board */}
          <Card className="mx-auto bg-gradient-to-br from-emerald-100/10 via-green-200/10 to-lime-100/10 shadow-2xl border-4 border-white/20 backdrop-blur-xl overflow-hidden relative">
            <div className="absolute inset-0 bg-gradient-to-br from-green-400/5 via-emerald-300/10 to-lime-400/5 animate-pulse-glow"></div>
            <div className="relative p-6">
              <div 
                className="grid gap-1 mx-auto bg-gradient-to-br from-emerald-900/30 via-green-800/20 to-lime-900/30 rounded-2xl p-6 shadow-inner relative overflow-hidden"
                style={{
                  gridTemplateColumns: `repeat(${GRID_SIZE}, 1fr)`,
                  gridTemplateRows: `repeat(${GRID_SIZE}, 1fr)`,
                  width: 'min(85vw, 650px)',
                  height: 'min(85vw, 650px)',
                }}
              >
                {/* Background pattern */}
                <div className="absolute inset-0 opacity-20">
                  {Array.from({ length: 10 }).map((_, i) => (
                    <div 
                      key={i} 
                      className="absolute bg-gradient-to-r from-green-400/10 to-emerald-400/10 rounded-full animate-float blur-sm"
                      style={{
                        width: `${Math.random() * 40 + 20}px`,
                        height: `${Math.random() * 40 + 20}px`,
                        left: `${Math.random() * 100}%`,
                        top: `${Math.random() * 100}%`,
                        animationDelay: `${Math.random() * 3}s`,
                      }}
                    ></div>
                  ))}
                </div>

                {Array.from({ length: GRID_SIZE * GRID_SIZE }).map((_, index) => {
                  const x = index % GRID_SIZE;
                  const y = Math.floor(index / GRID_SIZE);
                  const snakeSegmentIndex = gameState.snake.findIndex(segment => segment.x === x && segment.y === y);
                  const isSnakeHead = snakeSegmentIndex === 0;
                  const isSnakeBody = snakeSegmentIndex > 0;
                  const isEgg = gameState.egg.x === x && gameState.egg.y === y;
                  const hasParticle = particles.some(particle => 
                    Math.floor(particle.x) === x && Math.floor(particle.y) === y
                  );
                  const hasFloatingText = floatingTexts.some(ft => 
                    Math.floor(ft.x) === x && Math.floor(ft.y) === y
                  );

                  return (
                    <div
                      key={index}
                      className={`
                        aspect-square transition-all duration-300 relative
                        ${isSnakeHead || isSnakeBody ? getSnakeSegmentStyle(snakeSegmentIndex, isSnakeHead) : ''}
                        ${isEgg ? 'animate-bounce-gentle transform hover:scale-110' : ''}
                        ${(x + y) % 2 === 0 ? 'bg-gradient-to-br from-green-100/5 to-emerald-100/5' : 'bg-gradient-to-br from-lime-100/5 to-green-100/5'}
                        rounded-lg
                      `}
                    >
                      {isSnakeHead && (
                        <div className="w-full h-full flex items-center justify-center relative">
                          {/* Enhanced snake head */}
                          <div className="absolute inset-0 bg-gradient-to-br from-emerald-200/60 via-green-400/40 to-emerald-800/60 rounded-xl"></div>
                          
                          {/* Glowing eyes */}
                          <div className="absolute inset-0 flex items-center justify-center">
                            <div className="flex space-x-1.5">
                              <div className="w-2 h-2 bg-gradient-to-br from-white to-blue-200 rounded-full shadow-lg animate-pulse">
                                <div className="w-1 h-1 bg-black rounded-full ml-0.5 mt-0.5"></div>
                                <div className="absolute w-3 h-3 bg-blue-400/30 rounded-full -ml-0.5 -mt-1 blur-sm animate-ping"></div>
                              </div>
                              <div className="w-2 h-2 bg-gradient-to-br from-white to-blue-200 rounded-full shadow-lg animate-pulse">
                                <div className="w-1 h-1 bg-black rounded-full ml-0.5 mt-0.5"></div>
                                <div className="absolute w-3 h-3 bg-blue-400/30 rounded-full -ml-0.5 -mt-1 blur-sm animate-ping"></div>
                              </div>
                            </div>
                          </div>
                          
                          {/* Animated tongue */}
                          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1">
                            <div className="w-1 h-2 bg-gradient-to-b from-red-500 to-red-700 animate-wiggle origin-bottom rounded-sm shadow-lg"></div>
                          </div>
                          
                          {/* Head shine effects */}
                          <div className="absolute top-1 left-1 w-3 h-2 bg-white/60 rounded-full blur-sm animate-pulse"></div>
                          <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-transparent rounded-xl"></div>
                        </div>
                      )}
                      
                      {isSnakeBody && (
                        <div className="w-full h-full relative">
                          {/* Enhanced body with scales */}
                          <div className="absolute inset-0 bg-gradient-to-br from-green-200/40 via-green-500/30 to-green-800/40 rounded-lg"></div>
                          
                          {/* Scale pattern */}
                          <div className="absolute inset-0 flex items-center justify-center">
                            <div className="w-4 h-1.5 bg-green-600/50 rounded-full"></div>
                          </div>
                          <div className="absolute inset-0 flex items-center justify-center transform rotate-90">
                            <div className="w-4 h-1 bg-green-700/40 rounded-full"></div>
                          </div>
                          
                          {/* Body shine */}
                          <div className="absolute top-1 left-1 w-2 h-1 bg-white/40 rounded-full blur-sm"></div>
                          <div className="absolute inset-0 bg-gradient-to-br from-white/15 via-transparent to-transparent rounded-lg"></div>
                        </div>
                      )}
                      
                      {isEgg && (
                        <div className="w-full h-full flex items-center justify-center relative">
                          <div className="w-full h-full bg-gradient-to-br from-yellow-300 via-amber-400 to-orange-600 rounded-full shadow-2xl relative overflow-hidden animate-float">
                            {/* Multi-layer golden effect */}
                            <div className="absolute inset-0 bg-gradient-to-br from-yellow-100/80 via-transparent to-orange-900/20 rounded-full"></div>
                            <div className="absolute inset-0 bg-gradient-to-tr from-transparent via-white/30 to-transparent rounded-full"></div>
                            
                            {/* Enhanced sparkles */}
                            <div className="absolute top-1 right-1 w-1.5 h-1.5 bg-white rounded-full animate-ping"></div>
                            <div className="absolute bottom-2 left-1 w-1 h-1 bg-yellow-100 rounded-full animate-pulse"></div>
                            <div className="absolute top-3 left-2 w-0.5 h-0.5 bg-white rounded-full animate-bounce-gentle"></div>
                            
                            {/* Rotating glow effect */}
                            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent rounded-full animate-spin" style={{ animationDuration: '3s' }}></div>
                            
                            {/* Egg icon */}
                            <div className="absolute inset-0 flex items-center justify-center">
                              <Egg className="w-4/5 h-4/5 text-white/90 drop-shadow-lg" />
                            </div>
                          </div>
                        </div>
                      )}
                      
                      {/* Enhanced particles */}
                      {particles.filter(p => Math.floor(p.x) === x && Math.floor(p.y) === y).map(particle => (
                        <div key={particle.id} className="absolute inset-0 pointer-events-none">
                          {particle.type === 'sparkle' && (
                            <div className="w-full h-full bg-gradient-to-br from-yellow-300 via-amber-400 to-orange-500 rounded-full animate-ping opacity-80 shadow-xl"></div>
                          )}
                          {particle.type === 'star' && (
                            <div className="w-full h-full flex items-center justify-center">
                              <Star className="w-8 h-8 text-yellow-400 animate-spin" />
                            </div>
                          )}
                          {particle.type === 'explosion' && (
                            <div className="w-full h-full bg-gradient-to-br from-red-400 via-orange-500 to-red-600 rounded-full animate-ping opacity-90 shadow-2xl"></div>
                          )}
                        </div>
                      ))}

                      {/* Floating text */}
                      {floatingTexts.filter(ft => Math.floor(ft.x) === x && Math.floor(ft.y) === y).map(ft => (
                        <div key={ft.id} className="absolute inset-0 flex items-center justify-center pointer-events-none animate-slide-up">
                          <div className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg animate-bounce-gentle">
                            {ft.text}
                          </div>
                        </div>
                      ))}
                    </div>
                  );
                })}
              </div>

              {/* Enhanced Game Over Overlay */}
              {gameState.gameOver && (
                <div className="absolute inset-0 bg-gradient-to-br from-black/70 via-purple-900/60 to-black/70 backdrop-blur-lg flex items-center justify-center animate-slide-up">
                  <div className="bg-gradient-to-br from-white/95 to-white/90 rounded-3xl p-10 text-center shadow-2xl animate-pop max-w-md mx-4 relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-br from-red-100/20 via-orange-100/20 to-yellow-100/20 rounded-3xl"></div>
                    <div className="relative">
                      <div className="text-6xl mb-4 animate-bounce-gentle">💀</div>
                      <h2 className="text-4xl font-bold text-gray-800 mb-3">Game Over!</h2>
                      <div className="flex items-center justify-center space-x-2 mb-4">
                        <scoreRank.icon className={`w-6 h-6 ${scoreRank.color}`} />
                        <p className="text-2xl text-gray-700">Final Score: <span className="font-bold text-purple-600">{gameState.score}</span></p>
                      </div>
                      <p className={`text-lg mb-6 ${scoreRank.color} font-medium`}>
                        Rank: {scoreRank.rank}
                      </p>
                      <Button 
                        onClick={resetGame}
                        className="bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 hover:from-purple-700 hover:via-pink-700 hover:to-red-700 transform hover:scale-105 transition-all duration-300 text-white font-bold py-4 px-8 rounded-full shadow-2xl animate-bounce-gentle text-lg"
                      >
                        🎮 Play Again
                      </Button>
                    </div>
                  </div>
                </div>
              )}

              {/* Enhanced Start Screen */}
              {!gameState.gameStarted && !gameState.gameOver && (
                <div className="absolute inset-0 bg-gradient-to-br from-purple-900/80 via-blue-900/70 to-indigo-900/80 backdrop-blur-lg flex items-center justify-center animate-slide-up">
                  <div className="bg-gradient-to-br from-white/95 to-white/90 rounded-3xl p-12 text-center shadow-2xl animate-pop max-w-lg mx-4 relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-br from-green-100/20 via-blue-100/20 to-purple-100/20 rounded-3xl"></div>
                    <div className="relative">
                      <div className="text-6xl mb-6 animate-bounce-gentle">🎯</div>
                      <h2 className="text-4xl font-bold text-gray-800 mb-4">Ready to Slither?</h2>
                      <p className="text-gray-600 mb-2 text-lg">Use arrow keys or WASD to control your snake!</p>
                      <p className="text-gray-600 mb-8 text-base">Collect eggs quickly for combo multipliers!</p>
                      <Button 
                        onClick={startGame}
                        className="bg-gradient-to-r from-green-500 via-emerald-500 to-teal-500 hover:from-green-600 hover:via-emerald-600 hover:to-teal-600 transform hover:scale-105 transition-all duration-300 text-white font-bold py-4 px-10 rounded-full shadow-2xl animate-bounce-gentle text-xl"
                      >
                        🚀 Start Adventure
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </Card>

          {/* Enhanced Mobile Controls */}
          <div className="mt-8 flex justify-center">
            <div className="grid grid-cols-3 gap-3 bg-gradient-to-br from-white/20 to-white/10 backdrop-blur-xl rounded-3xl p-6 lg:hidden border border-white/20 shadow-2xl">
              <div></div>
              <Button
                onClick={() => changeDirection('UP')}
                className="bg-gradient-to-br from-white/30 to-white/20 hover:from-white/40 hover:to-white/30 text-white border-none rounded-2xl h-14 w-14 text-2xl transform hover:scale-110 transition-all duration-200 shadow-lg"
                disabled={!gameState.gameStarted || gameState.gameOver}
              >
                ⬆️
              </Button>
              <div></div>
              <Button
                onClick={() => changeDirection('LEFT')}
                className="bg-gradient-to-br from-white/30 to-white/20 hover:from-white/40 hover:to-white/30 text-white border-none rounded-2xl h-14 w-14 text-2xl transform hover:scale-110 transition-all duration-200 shadow-lg"
                disabled={!gameState.gameStarted || gameState.gameOver}
              >
                ⬅️
              </Button>
              <div></div>
              <Button
                onClick={() => changeDirection('RIGHT')}
                className="bg-gradient-to-br from-white/30 to-white/20 hover:from-white/40 hover:to-white/30 text-white border-none rounded-2xl h-14 w-14 text-2xl transform hover:scale-110 transition-all duration-200 shadow-lg"
                disabled={!gameState.gameStarted || gameState.gameOver}
              >
                ➡️
              </Button>
              <div></div>
              <Button
                onClick={() => changeDirection('DOWN')}
                className="bg-gradient-to-br from-white/30 to-white/20 hover:from-white/40 hover:to-white/30 text-white border-none rounded-2xl h-14 w-14 text-2xl transform hover:scale-110 transition-all duration-200 shadow-lg"
                disabled={!gameState.gameStarted || gameState.gameOver}
              >
                ⬇️
              </Button>
              <div></div>
            </div>
          </div>

          {/* Enhanced Instructions */}
          <div className="text-center mt-8 animate-slide-up">
            <div className="inline-block bg-gradient-to-r from-white/10 to-white/5 backdrop-blur-xl rounded-2xl px-8 py-4 border border-white/20">
              <p className="text-white/90 text-lg font-medium">
                🎮 Arrow keys or WASD to move • 🥚 Collect eggs quickly for combos • 💀 Avoid walls and yourself!
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SnakeGame;
